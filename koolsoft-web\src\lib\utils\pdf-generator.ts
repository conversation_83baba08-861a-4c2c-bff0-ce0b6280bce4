/**
 * PDF Generation Utility for Quotations
 * 
 * This utility provides functions to generate PDF documents for quotations
 * using jsPDF library for client-side PDF generation.
 */

import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

interface QuotationData {
  id: string;
  quotationNumber: string;
  quotationDate: string;
  validUntil?: string;
  status: string;
  subject?: string;
  notes?: string;
  termsConditions?: string;
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  discount?: number;
  discountType?: string;
  customer: {
    name: string;
    email?: string;
    phone?: string;
    mobile?: string;
    address?: string;
    city?: string;
    state?: string;
    pinCode?: string;
  };
  executive: {
    name: string;
    email?: string;
    phone?: string;
    designation?: string;
  };
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    taxRate?: number;
    taxAmount?: number;
    discount?: number;
    specifications?: string;
  }>;
}

/**
 * Generate PDF for a quotation
 * @param quotation Quotation data
 * @returns Promise resolving to PDF blob
 */
export async function generateQuotationPDF(quotation: QuotationData): Promise<Blob> {
  const doc = new jsPDF();
  
  // Company header
  doc.setFontSize(20);
  doc.setTextColor(15, 82, 186); // Primary blue color
  doc.text('KoolSoft Technologies', 20, 25);
  
  doc.setFontSize(10);
  doc.setTextColor(100, 100, 100);
  doc.text('Air Conditioning & Refrigeration Solutions', 20, 32);
  doc.text('Email: <EMAIL> | Phone: +91-XXXXXXXXXX', 20, 38);
  
  // Quotation title
  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text('QUOTATION', 20, 55);
  
  // Quotation details
  doc.setFontSize(10);
  const quotationDetails = [
    ['Quotation Number:', quotation.quotationNumber],
    ['Date:', new Date(quotation.quotationDate).toLocaleDateString()],
    ['Valid Until:', quotation.validUntil ? new Date(quotation.validUntil).toLocaleDateString() : 'N/A'],
    ['Status:', quotation.status],
  ];
  
  let yPos = 65;
  quotationDetails.forEach(([label, value]) => {
    doc.text(label, 20, yPos);
    doc.text(value, 80, yPos);
    yPos += 6;
  });
  
  // Customer details
  doc.setFontSize(12);
  doc.text('Bill To:', 20, yPos + 10);
  
  doc.setFontSize(10);
  yPos += 20;
  doc.text(quotation.customer.name, 20, yPos);
  
  if (quotation.customer.address) {
    yPos += 6;
    doc.text(quotation.customer.address, 20, yPos);
  }
  
  if (quotation.customer.city || quotation.customer.state || quotation.customer.pinCode) {
    yPos += 6;
    const location = [
      quotation.customer.city,
      quotation.customer.state,
      quotation.customer.pinCode
    ].filter(Boolean).join(', ');
    doc.text(location, 20, yPos);
  }
  
  if (quotation.customer.phone) {
    yPos += 6;
    doc.text(`Phone: ${quotation.customer.phone}`, 20, yPos);
  }
  
  if (quotation.customer.email) {
    yPos += 6;
    doc.text(`Email: ${quotation.customer.email}`, 20, yPos);
  }
  
  // Executive details (right side)
  doc.setFontSize(12);
  doc.text('Sales Executive:', 120, yPos - (quotation.customer.email ? 24 : 18));
  
  doc.setFontSize(10);
  let execYPos = yPos - (quotation.customer.email ? 14 : 8);
  doc.text(quotation.executive.name, 120, execYPos);
  
  if (quotation.executive.designation) {
    execYPos += 6;
    doc.text(quotation.executive.designation, 120, execYPos);
  }
  
  if (quotation.executive.email) {
    execYPos += 6;
    doc.text(quotation.executive.email, 120, execYPos);
  }
  
  if (quotation.executive.phone) {
    execYPos += 6;
    doc.text(quotation.executive.phone, 120, execYPos);
  }
  
  // Subject
  if (quotation.subject) {
    yPos += 20;
    doc.setFontSize(12);
    doc.text('Subject:', 20, yPos);
    doc.setFontSize(10);
    doc.text(quotation.subject, 20, yPos + 8);
    yPos += 8;
  }
  
  // Items table
  yPos += 20;
  const tableColumns = [
    { header: 'Description', dataKey: 'description' },
    { header: 'Qty', dataKey: 'quantity' },
    { header: 'Unit Price', dataKey: 'unitPrice' },
    { header: 'Tax %', dataKey: 'taxRate' },
    { header: 'Total', dataKey: 'totalPrice' },
  ];
  
  const tableRows = quotation.items.map(item => ({
    description: item.description + (item.specifications ? `\n${item.specifications}` : ''),
    quantity: item.quantity.toString(),
    unitPrice: `₹${item.unitPrice.toLocaleString()}`,
    taxRate: `${item.taxRate || 0}%`,
    totalPrice: `₹${item.totalPrice.toLocaleString()}`,
  }));
  
  doc.autoTable({
    startY: yPos,
    columns: tableColumns,
    body: tableRows,
    theme: 'grid',
    headStyles: {
      fillColor: [15, 82, 186],
      textColor: [255, 255, 255],
      fontSize: 10,
    },
    bodyStyles: {
      fontSize: 9,
    },
    columnStyles: {
      description: { cellWidth: 80 },
      quantity: { cellWidth: 20, halign: 'center' },
      unitPrice: { cellWidth: 30, halign: 'right' },
      taxRate: { cellWidth: 20, halign: 'center' },
      totalPrice: { cellWidth: 30, halign: 'right' },
    },
  });
  
  // Get the final Y position after the table
  const finalY = (doc as any).lastAutoTable.finalY || yPos + 50;
  
  // Totals
  const totalsY = finalY + 10;
  const totalsX = 130;
  
  doc.setFontSize(10);
  doc.text('Subtotal:', totalsX, totalsY);
  doc.text(`₹${quotation.subtotal.toLocaleString()}`, totalsX + 40, totalsY);
  
  if (quotation.discount && quotation.discount > 0) {
    doc.text('Discount:', totalsX, totalsY + 6);
    const discountText = quotation.discountType === 'PERCENTAGE' 
      ? `${quotation.discount}%` 
      : `₹${quotation.discount.toLocaleString()}`;
    doc.text(discountText, totalsX + 40, totalsY + 6);
  }
  
  doc.text('Tax Amount:', totalsX, totalsY + 12);
  doc.text(`₹${quotation.taxAmount.toLocaleString()}`, totalsX + 40, totalsY + 12);
  
  // Total with border
  doc.setLineWidth(0.5);
  doc.line(totalsX, totalsY + 16, totalsX + 60, totalsY + 16);
  
  doc.setFontSize(12);
  doc.setFont(undefined, 'bold');
  doc.text('Total Amount:', totalsX, totalsY + 22);
  doc.text(`₹${quotation.totalAmount.toLocaleString()}`, totalsX + 40, totalsY + 22);
  
  // Notes
  if (quotation.notes) {
    doc.setFont(undefined, 'normal');
    doc.setFontSize(10);
    doc.text('Notes:', 20, totalsY + 35);
    
    // Split notes into multiple lines if needed
    const splitNotes = doc.splitTextToSize(quotation.notes, 170);
    doc.text(splitNotes, 20, totalsY + 42);
  }
  
  // Terms and conditions
  if (quotation.termsConditions) {
    const termsY = totalsY + (quotation.notes ? 60 : 45);
    doc.setFontSize(10);
    doc.text('Terms & Conditions:', 20, termsY);
    
    const splitTerms = doc.splitTextToSize(quotation.termsConditions, 170);
    doc.text(splitTerms, 20, termsY + 7);
  }
  
  // Footer
  const pageHeight = doc.internal.pageSize.height;
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text('Thank you for your business!', 20, pageHeight - 20);
  doc.text(`Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 20, pageHeight - 15);
  
  // Convert to blob
  const pdfBlob = doc.output('blob');
  return pdfBlob;
}

/**
 * Download quotation as PDF
 * @param quotation Quotation data
 * @param filename Optional filename (defaults to quotation number)
 */
export async function downloadQuotationPDF(
  quotation: QuotationData,
  filename?: string
): Promise<void> {
  const pdfBlob = await generateQuotationPDF(quotation);
  const url = URL.createObjectURL(pdfBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename || `quotation-${quotation.quotationNumber}.pdf`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
}

/**
 * Preview quotation PDF in new window
 * @param quotation Quotation data
 */
export async function previewQuotationPDF(quotation: QuotationData): Promise<void> {
  const pdfBlob = await generateQuotationPDF(quotation);
  const url = URL.createObjectURL(pdfBlob);
  
  window.open(url, '_blank');
  
  // Clean up the URL after a delay
  setTimeout(() => {
    URL.revokeObjectURL(url);
  }, 1000);
}
