import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Quotation Repository
 *
 * This repository handles database operations for the Quotation entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class QuotationRepository extends PrismaRepository<
  Prisma.quotationsGetPayload<{
    include: {
      customer: true;
      executive: true;
      items: {
        include: {
          product: true;
          model: true;
          brand: true;
        };
      };
    };
  }>,
  string,
  Prisma.quotationsCreateInput,
  Prisma.quotationsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('quotations');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: any): QuotationRepository {
    return new QuotationRepository(tx);
  }

  /**
   * Generate a unique quotation number
   * @returns Promise resolving to a unique quotation number
   */
  async generateQuotationNumber(): Promise<string> {
    const currentYear = new Date().getFullYear();
    const prefix = `QT${currentYear}-`;
    
    // Find the highest existing number for this year
    const lastQuotation = await this.model.findFirst({
      where: {
        quotationNumber: {
          startsWith: prefix,
        },
      },
      orderBy: {
        quotationNumber: 'desc',
      },
    });

    let nextNumber = 1;
    if (lastQuotation?.quotationNumber) {
      const lastNumber = parseInt(lastQuotation.quotationNumber.split('-')[1] || '0');
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
  }

  /**
   * Find quotations with all related data
   * @param filter Filter criteria
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Sorting criteria
   * @returns Promise resolving to an array of quotations with related data
   */
  async findWithRelations(
    filter: Prisma.quotationsWhereInput = {},
    skip?: number,
    take?: number,
    orderBy?: Prisma.quotationsOrderByWithRelationInput
  ) {
    try {
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { quotationDate: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              city: true,
              address: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
              designation: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
              model: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  specs: true,
                  tonnage: true,
                  bslMRP: true,
                  bslMCP: true,
                  taplMRP: true,
                  taplMCP: true,
                },
              },
              brand: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
            orderBy: {
              sortOrder: 'asc',
            },
          },
        },
      });

      return result;
    } catch (error) {
      console.error('QuotationRepository.findWithRelations: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Find a single quotation with all related data
   * @param id Quotation ID
   * @returns Promise resolving to quotation with related data or null
   */
  async findWithAllRelations(id: string) {
    try {
      return await this.model.findUnique({
        where: { id },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              mobile: true,
              city: true,
              address: true,
              state: true,
              pinCode: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              designation: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
              model: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  specs: true,
                  tonnage: true,
                  bslMRP: true,
                  bslMCP: true,
                  bslCP: true,
                  taplMRP: true,
                  taplMCP: true,
                  taplCP: true,
                  installCharge: true,
                  numberOfComponents: true,
                },
              },
              brand: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
            orderBy: {
              sortOrder: 'asc',
            },
          },
        },
      });
    } catch (error) {
      console.error('QuotationRepository.findWithAllRelations: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Find quotations by customer ID
   * @param customerId Customer ID
   * @param options Query options
   * @returns Promise resolving to quotations
   */
  async findByCustomerId(
    customerId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { customerId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
            items: {
              select: {
                id: true,
                description: true,
                quantity: true,
                unitPrice: true,
                totalPrice: true,
              },
            },
          }
        : undefined,
      orderBy: { quotationDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Find quotations by executive ID
   * @param executiveId Executive ID
   * @param options Query options
   * @returns Promise resolving to quotations
   */
  async findByExecutiveId(
    executiveId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { executiveId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
            items: {
              select: {
                id: true,
                description: true,
                quantity: true,
                unitPrice: true,
                totalPrice: true,
              },
            },
          }
        : undefined,
      orderBy: { quotationDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Search quotations by various criteria
   * @param searchTerm Search term
   * @param options Query options
   * @returns Promise resolving to matching quotations
   */
  async search(
    searchTerm: string,
    options?: {
      skip?: number;
      take?: number;
      status?: string;
      customerId?: string;
      executiveId?: string;
    }
  ) {
    const { skip = 0, take = 10, status, customerId, executiveId } = options || {};

    const whereClause: Prisma.quotationsWhereInput = {
      AND: [
        // Search term filter
        {
          OR: [
            {
              quotationNumber: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
            {
              subject: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
            {
              notes: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
            {
              customer: {
                name: {
                  contains: searchTerm,
                  mode: 'insensitive',
                },
              },
            },
            {
              contactPerson: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
          ],
        },
        // Additional filters
        ...(status ? [{ status }] : []),
        ...(customerId ? [{ customerId }] : []),
        ...(executiveId ? [{ executiveId }] : []),
      ],
    };

    return this.findWithRelations(whereClause, skip, take);
  }

  /**
   * Count quotations matching search criteria
   * @param searchTerm Search term
   * @param options Filter options
   * @returns Promise resolving to the count of matching quotations
   */
  async countSearch(
    searchTerm: string,
    options?: {
      status?: string;
      customerId?: string;
      executiveId?: string;
    }
  ): Promise<number> {
    const { status, customerId, executiveId } = options || {};

    const whereClause: Prisma.quotationsWhereInput = {
      AND: [
        // Search term filter
        {
          OR: [
            {
              quotationNumber: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
            {
              subject: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
            {
              notes: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
            {
              customer: {
                name: {
                  contains: searchTerm,
                  mode: 'insensitive',
                },
              },
            },
            {
              contactPerson: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
          ],
        },
        // Additional filters
        ...(status ? [{ status }] : []),
        ...(customerId ? [{ customerId }] : []),
        ...(executiveId ? [{ executiveId }] : []),
      ],
    };

    return this.model.count({
      where: whereClause,
    });
  }

  /**
   * Get quotation statistics
   * @param options Filter options
   * @returns Promise resolving to quotation statistics
   */
  async getStatistics(options?: {
    customerId?: string;
    executiveId?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { customerId, executiveId, startDate, endDate } = options || {};

    const whereClause: Prisma.quotationsWhereInput = {
      AND: [
        ...(customerId ? [{ customerId }] : []),
        ...(executiveId ? [{ executiveId }] : []),
        ...(startDate || endDate
          ? [
              {
                quotationDate: {
                  ...(startDate ? { gte: startDate } : {}),
                  ...(endDate ? { lte: endDate } : {}),
                },
              },
            ]
          : []),
      ],
    };

    const [
      totalCount,
      draftCount,
      sentCount,
      acceptedCount,
      rejectedCount,
      expiredCount,
      totalValue,
    ] = await Promise.all([
      this.model.count({ where: whereClause }),
      this.model.count({ where: { ...whereClause, status: 'DRAFT' } }),
      this.model.count({ where: { ...whereClause, status: 'SENT' } }),
      this.model.count({ where: { ...whereClause, status: 'ACCEPTED' } }),
      this.model.count({ where: { ...whereClause, status: 'REJECTED' } }),
      this.model.count({ where: { ...whereClause, status: 'EXPIRED' } }),
      this.model.aggregate({
        where: whereClause,
        _sum: {
          totalAmount: true,
        },
      }),
    ]);

    return {
      totalCount,
      statusCounts: {
        draft: draftCount,
        sent: sentCount,
        accepted: acceptedCount,
        rejected: rejectedCount,
        expired: expiredCount,
      },
      totalValue: totalValue._sum.totalAmount || 0,
    };
  }
}
