'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  FileContract,
  Shield,
  Wrench,
  TrendingUp,
  Users,
  DollarSign,
  BarChart,
  Search,
  Filter,
  Eye,
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ReportType {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  permissions: string[];
  availableFilters: string[];
  sortOptions: Array<{ value: string; label: string }>;
  exportFormats: string[];
}

const iconMap = {
  FileContrac<PERSON>,
  <PERSON>,
  <PERSON>ch,
  TrendingUp,
  <PERSON>,
  DollarSign,
  <PERSON><PERSON><PERSON>,
};

/**
 * Report Viewer Main Page
 * 
 * Provides a centralized interface for selecting and viewing different types of reports.
 * Users can browse available reports by category, search for specific reports,
 * and navigate to detailed report viewers.
 */
export default function ReportViewerPage() {
  const [reportTypes, setReportTypes] = useState<ReportType[]>([]);
  const [filteredReports, setFilteredReports] = useState<ReportType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [categories, setCategories] = useState<string[]>([]);
  
  const router = useRouter();
  const { toast } = useToast();

  // Fetch available report types
  useEffect(() => {
    const fetchReportTypes = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/reports/types', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch report types');
        }

        const data = await response.json();
        setReportTypes(data.data || []);
        
        // Extract unique categories
        const uniqueCategories = Array.from(
          new Set((data.data || []).map((report: ReportType) => report.category))
        );
        setCategories(uniqueCategories);
        
      } catch (error) {
        console.error('Error fetching report types:', error);
        toast({
          title: 'Error',
          description: 'Failed to load report types. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchReportTypes();
  }, [toast]);

  // Filter reports based on search and category
  useEffect(() => {
    let filtered = reportTypes;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(report => report.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(report =>
        report.name.toLowerCase().includes(query) ||
        report.description.toLowerCase().includes(query) ||
        report.category.toLowerCase().includes(query)
      );
    }

    setFilteredReports(filtered);
  }, [reportTypes, searchQuery, selectedCategory]);

  const handleViewReport = (reportType: string) => {
    router.push(`/reports/viewer/${reportType.toLowerCase()}`);
  };

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap];
    return IconComponent ? <IconComponent className="h-6 w-6" /> : <BarChart className="h-6 w-6" />;
  };

  const getRoleColor = (permissions: string[]) => {
    if (permissions.includes('USER')) return 'bg-green-100 text-green-800';
    if (permissions.includes('EXECUTIVE')) return 'bg-blue-100 text-blue-800';
    if (permissions.includes('MANAGER')) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800'; // ADMIN only
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 w-48" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search reports..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Report Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredReports.map((report) => (
          <Card key={report.type} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 bg-primary rounded-md p-2 text-white">
                    {getIcon(report.icon)}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{report.name}</CardTitle>
                    <Badge variant="secondary" className="mt-1">
                      {report.category}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <CardDescription className="text-sm">
                {report.description}
              </CardDescription>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Access Level:</span>
                  <Badge 
                    variant="outline" 
                    className={getRoleColor(report.permissions)}
                  >
                    {report.permissions.includes('USER') ? 'All Users' : 
                     report.permissions.includes('EXECUTIVE') ? 'Executive+' :
                     report.permissions.includes('MANAGER') ? 'Manager+' : 'Admin Only'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Export Formats:</span>
                  <span>{report.exportFormats.join(', ')}</span>
                </div>
              </div>

              <Button 
                onClick={() => handleViewReport(report.type)}
                className="w-full"
                size="sm"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Report
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* No Results */}
      {filteredReports.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <BarChart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No reports found</h3>
          <p className="text-gray-500">
            {searchQuery || selectedCategory !== 'all'
              ? 'Try adjusting your search or filter criteria.'
              : 'No reports are available at this time.'}
          </p>
        </div>
      )}
    </div>
  );
}
