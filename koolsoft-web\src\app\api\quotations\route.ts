import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getQuotationRepository, getQuotationItemRepository } from '@/lib/repositories';
import { createQuotationSchema, quotationFilterSchema } from '@/lib/validations/quotation.schema';
import { z } from 'zod';

/**
 * GET /api/quotations
 * Get quotations with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const queryParams = Object.fromEntries(searchParams.entries());
      const validatedParams = quotationFilterSchema.parse(queryParams);

      const {
        search,
        status,
        customerId,
        executiveId,
        startDate,
        endDate,
        page,
        limit,
        sortBy,
        sortOrder,
      } = validatedParams;

      const skip = (page - 1) * limit;

      // Build filter criteria
      const whereClause: any = {
        AND: [
          // Date range filter
          ...(startDate || endDate
            ? [
                {
                  quotationDate: {
                    ...(startDate ? { gte: startDate } : {}),
                    ...(endDate ? { lte: endDate } : {}),
                  },
                },
              ]
            : []),
          // Status filter
          ...(status ? [{ status }] : []),
          // Customer filter
          ...(customerId ? [{ customerId }] : []),
          // Executive filter
          ...(executiveId ? [{ executiveId }] : []),
        ],
      };

      const quotationRepository = getQuotationRepository();

      // Get quotations with search or filter
      const quotations = search
        ? await quotationRepository.search(search, {
            skip,
            take: limit,
            status,
            customerId,
            executiveId,
          })
        : await quotationRepository.findWithRelations(
            whereClause,
            skip,
            limit,
            { [sortBy]: sortOrder }
          );

      // Get total count for pagination
      const totalCount = search
        ? await quotationRepository.countSearch(search, {
            status,
            customerId,
            executiveId,
          })
        : await quotationRepository.count(whereClause);

      const totalPages = Math.ceil(totalCount / limit);

      return NextResponse.json({
        success: true,
        data: quotations,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      });
    } catch (error) {
      console.error('Error fetching quotations:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid query parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch quotations',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/quotations
 * Create a new quotation
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = createQuotationSchema.parse(body);

      const quotationRepository = getQuotationRepository();
      const quotationItemRepository = getQuotationItemRepository();

      // Generate quotation number
      const quotationNumber = await quotationRepository.generateQuotationNumber();

      // Calculate totals from items
      let subtotal = 0;
      let taxAmount = 0;
      
      const processedItems = validatedData.items.map((item, index) => {
        const itemTotalPrice = item.quantity * item.unitPrice;
        const itemTaxAmount = (itemTotalPrice * (item.taxRate || 0)) / 100;
        
        subtotal += itemTotalPrice;
        taxAmount += itemTaxAmount;

        return {
          ...item,
          totalPrice: itemTotalPrice,
          taxAmount: itemTaxAmount,
          sortOrder: item.sortOrder || index,
        };
      });

      const totalAmount = subtotal + taxAmount;

      // Create quotation with items in a transaction
      const result = await quotationRepository.executeInTransaction(async (tx) => {
        // Create quotation
        const quotation = await tx.quotations.create({
          data: {
            quotationNumber,
            customerId: validatedData.customerId,
            executiveId: validatedData.executiveId,
            quotationDate: validatedData.quotationDate,
            validUntil: validatedData.validUntil,
            status: validatedData.status,
            contactPerson: validatedData.contactPerson,
            contactPhone: validatedData.contactPhone,
            contactEmail: validatedData.contactEmail,
            subject: validatedData.subject,
            notes: validatedData.notes,
            termsConditions: validatedData.termsConditions,
            subtotal,
            taxAmount,
            totalAmount,
            discount: validatedData.discount || 0,
            discountType: validatedData.discountType || 'PERCENTAGE',
          },
        });

        // Create quotation items
        const items = await Promise.all(
          processedItems.map((item) =>
            tx.quotation_items.create({
              data: {
                quotationId: quotation.id,
                productId: item.productId,
                modelId: item.modelId,
                brandId: item.brandId,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                taxRate: item.taxRate || 0,
                taxAmount: item.taxAmount || 0,
                discount: item.discount || 0,
                discountType: item.discountType || 'PERCENTAGE',
                specifications: item.specifications,
                notes: item.notes,
                sortOrder: item.sortOrder,
              },
            })
          )
        );

        return { quotation, items };
      });

      // Fetch the complete quotation with relations
      const completeQuotation = await quotationRepository.findWithAllRelations(
        result.quotation.id
      );

      return NextResponse.json({
        success: true,
        data: completeQuotation,
        message: 'Quotation created successfully',
      });
    } catch (error) {
      console.error('Error creating quotation:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid quotation data',
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create quotation',
        },
        { status: 500 }
      );
    }
  }
);
